#!/usr/bin/env python3
"""
Google Cloud Audio Manager
Spr<PERSON>va a prehrávanie audio súborov z Google Cloud Storage
"""

import os
import sys
import subprocess
import json
from pathlib import Path
import webbrowser
from http.server import HTTPServer, SimpleHTTPRequestHandler
import threading
import time

class CloudAudioManager:
    def __init__(self):
        self.project_id = "podcast-central-xl9zv"
        self.bucket_name = "vanhelsing"
        self.local_dir = Path(".")
        
    def check_gcloud_auth(self):
        """Kontrola autentifikácie Google Cloud"""
        try:
            result = subprocess.run(['gcloud', 'auth', 'list'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Google Cloud autentifikácia je aktívna")
                return True
            else:
                print("❌ Google Cloud autentifikácia zlyhala")
                return False
        except FileNotFoundError:
            print("❌ Google Cloud SDK nie je nainštalované")
            return False
    
    def set_project(self):
        """Nastavenie aktívneho projektu"""
        try:
            subprocess.run(['gcloud', 'config', 'set', 'project', self.project_id], 
                          check=True)
            print(f"✅ Projekt nastavený na: {self.project_id}")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ Chyba pri nastavovaní projektu: {self.project_id}")
            return False
    
    def list_audio_files(self):
        """Zoznam všetkých audio súborov v bucket"""
        try:
            result = subprocess.run([
                'gsutil', 'ls', '-r', f'gs://{self.bucket_name}/**/*.mp3',
                f'gs://{self.bucket_name}/**/*.wav',
                f'gs://{self.bucket_name}/**/*.m4a'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                files = [line.strip() for line in result.stdout.split('\n') if line.strip()]
                print(f"📁 Nájdených {len(files)} audio súborov:")
                for file in files:
                    print(f"  🎵 {file}")
                return files
            else:
                print("❌ Chyba pri načítavaní zoznamu súborov")
                return []
        except Exception as e:
            print(f"❌ Chyba: {e}")
            return []
    
    def download_file(self, gs_path, local_name=None):
        """Stiahnutie súboru z Google Cloud Storage"""
        if not local_name:
            local_name = os.path.basename(gs_path)
        
        try:
            print(f"📥 Sťahujem: {gs_path}")
            subprocess.run(['gsutil', 'cp', gs_path, local_name], check=True)
            print(f"✅ Súbor stiahnutý: {local_name}")
            return True
        except subprocess.CalledProcessError:
            print(f"❌ Chyba pri sťahovaní: {gs_path}")
            return False
    
    def download_all_audio(self):
        """Stiahnutie všetkých audio súborov"""
        files = self.list_audio_files()
        if not files:
            print("❌ Žiadne audio súbory nenájdené")
            return
        
        print(f"📥 Sťahujem {len(files)} súborov...")
        success_count = 0
        
        for file in files:
            local_name = os.path.basename(file)
            if self.download_file(file, local_name):
                success_count += 1
        
        print(f"✅ Úspešne stiahnutých: {success_count}/{len(files)} súborov")
    
    def create_public_urls(self):
        """Vytvorenie verejných URL pre audio súbory"""
        files = self.list_audio_files()
        public_urls = []
        
        for file in files:
            try:
                # Nastavenie verejného prístupu
                subprocess.run(['gsutil', 'acl', 'ch', '-u', 'AllUsers:R', file], 
                             check=True, capture_output=True)
                
                # Získanie verejnej URL
                public_url = file.replace('gs://', 'https://storage.googleapis.com/')
                public_urls.append({
                    'name': os.path.basename(file),
                    'gs_path': file,
                    'public_url': public_url
                })
                print(f"🌐 Verejná URL: {public_url}")
                
            except subprocess.CalledProcessError:
                print(f"❌ Chyba pri vytváraní verejnej URL pre: {file}")
        
        return public_urls
    
    def start_local_server(self, port=8000):
        """Spustenie lokálneho HTTP servera"""
        class CustomHandler(SimpleHTTPRequestHandler):
            def end_headers(self):
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                super().end_headers()
        
        try:
            server = HTTPServer(('localhost', port), CustomHandler)
            print(f"🌐 HTTP server spustený na: http://localhost:{port}")
            
            # Otvorenie prehliadača
            threading.Timer(1.0, lambda: webbrowser.open(f'http://localhost:{port}')).start()
            
            server.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Server zastavený")
            server.shutdown()
    
    def play_audio_with_system(self, filename):
        """Prehranie audio súboru systémovým prehrávačom"""
        if not os.path.exists(filename):
            print(f"❌ Súbor neexistuje: {filename}")
            return False
        
        try:
            if sys.platform == "darwin":  # macOS
                subprocess.run(['open', filename])
            elif sys.platform == "win32":  # Windows
                os.startfile(filename)
            else:  # Linux
                subprocess.run(['xdg-open', filename])
            
            print(f"🎵 Prehrávam: {filename}")
            return True
        except Exception as e:
            print(f"❌ Chyba pri prehrávaní: {e}")
            return False

def main():
    manager = CloudAudioManager()
    
    print("🎵 Google Cloud Audio Manager")
    print("=" * 40)
    
    # Kontrola autentifikácie
    if not manager.check_gcloud_auth():
        print("❌ Najprv sa prihláste: gcloud auth login")
        return
    
    # Nastavenie projektu
    if not manager.set_project():
        return
    
    while True:
        print("\n📋 Dostupné akcie:")
        print("1. 📁 Zobraziť zoznam audio súborov")
        print("2. 📥 Stiahnuť konkrétny súbor")
        print("3. 📥 Stiahnuť všetky audio súbory")
        print("4. 🌐 Vytvoriť verejné URL")
        print("5. 🌐 Spustiť lokálny web server")
        print("6. 🎵 Prehrať lokálny súbor")
        print("0. 🚪 Ukončiť")
        
        choice = input("\n👉 Vyberte akciu (0-6): ").strip()
        
        if choice == "1":
            manager.list_audio_files()
        
        elif choice == "2":
            files = manager.list_audio_files()
            if files:
                print("\n📋 Dostupné súbory:")
                for i, file in enumerate(files, 1):
                    print(f"{i}. {os.path.basename(file)}")
                
                try:
                    idx = int(input("👉 Vyberte číslo súboru: ")) - 1
                    if 0 <= idx < len(files):
                        manager.download_file(files[idx])
                    else:
                        print("❌ Neplatné číslo")
                except ValueError:
                    print("❌ Zadajte platné číslo")
        
        elif choice == "3":
            manager.download_all_audio()
        
        elif choice == "4":
            manager.create_public_urls()
        
        elif choice == "5":
            print("🌐 Spúšťam lokálny server...")
            print("💡 Stlačte Ctrl+C pre zastavenie")
            manager.start_local_server()
        
        elif choice == "6":
            local_files = [f for f in os.listdir('.') if f.endswith(('.mp3', '.wav', '.m4a'))]
            if local_files:
                print("\n📋 Lokálne audio súbory:")
                for i, file in enumerate(local_files, 1):
                    print(f"{i}. {file}")
                
                try:
                    idx = int(input("👉 Vyberte číslo súboru: ")) - 1
                    if 0 <= idx < len(local_files):
                        manager.play_audio_with_system(local_files[idx])
                    else:
                        print("❌ Neplatné číslo")
                except ValueError:
                    print("❌ Zadajte platné číslo")
            else:
                print("❌ Žiadne lokálne audio súbory nenájdené")
        
        elif choice == "0":
            print("👋 Dovidenia!")
            break
        
        else:
            print("❌ Neplatná voľba")

if __name__ == "__main__":
    main()
