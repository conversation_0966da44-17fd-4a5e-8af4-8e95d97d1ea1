#!/bin/bash

# Google Cloud Audio Manager Script
# Správa audio súborov z Google Cloud Storage

# Konfigurácia
PROJECT_ID="podcast-central-xl9zv"
BUCKET_NAME="vanhelsing"
AUDIO_DIR="./audio"

# Farby pre výstup
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Funkcie
print_header() {
    echo -e "${PURPLE}🎵 Google Cloud Audio Manager${NC}"
    echo -e "${PURPLE}=================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Kontrola Google Cloud SDK
check_gcloud() {
    if ! command -v gcloud &> /dev/null; then
        print_error "Google Cloud SDK nie je nainštalované"
        print_info "Nainštalujte ho z: https://cloud.google.com/sdk/docs/install"
        exit 1
    fi
    
    if ! command -v gsutil &> /dev/null; then
        print_error "gsutil nie je dostupné"
        exit 1
    fi
    
    print_success "Google Cloud SDK je dostupné"
}

# Nastavenie projektu
setup_project() {
    print_info "Nastavujem projekt: $PROJECT_ID"
    gcloud config set project $PROJECT_ID
    
    if [ $? -eq 0 ]; then
        print_success "Projekt nastavený"
    else
        print_error "Chyba pri nastavovaní projektu"
        exit 1
    fi
}

# Zoznam audio súborov
list_audio_files() {
    print_info "Načítavam zoznam audio súborov..."
    
    echo -e "\n${CYAN}📁 Audio súbory v bucket gs://$BUCKET_NAME:${NC}"
    gsutil ls -r gs://$BUCKET_NAME/**/*.mp3 gs://$BUCKET_NAME/**/*.wav gs://$BUCKET_NAME/**/*.m4a 2>/dev/null | while read file; do
        if [ ! -z "$file" ]; then
            filename=$(basename "$file")
            echo -e "  🎵 $filename"
        fi
    done
}

# Stiahnutie konkrétneho súboru
download_file() {
    local gs_path="$1"
    local local_name="$2"
    
    if [ -z "$local_name" ]; then
        local_name=$(basename "$gs_path")
    fi
    
    print_info "Sťahujem: $gs_path"
    gsutil cp "$gs_path" "$local_name"
    
    if [ $? -eq 0 ]; then
        print_success "Súbor stiahnutý: $local_name"
    else
        print_error "Chyba pri sťahovaní súboru"
    fi
}

# Stiahnutie všetkých audio súborov
download_all_audio() {
    print_info "Sťahujem všetky audio súbory..."
    
    # Vytvorenie adresára
    mkdir -p "$AUDIO_DIR"
    
    # Stiahnutie MP3 súborov
    gsutil -m cp -r gs://$BUCKET_NAME/ZVUKY/ "$AUDIO_DIR/" 2>/dev/null
    
    # Stiahnutie WAV súborov
    gsutil cp gs://$BUCKET_NAME/*.wav "$AUDIO_DIR/" 2>/dev/null
    
    print_success "Audio súbory stiahnuté do: $AUDIO_DIR"
}

# Prehranie audio súboru
play_audio() {
    local filename="$1"
    
    if [ ! -f "$filename" ]; then
        print_error "Súbor neexistuje: $filename"
        return 1
    fi
    
    print_info "Prehrávam: $filename"
    
    # Detekcia operačného systému a prehranie
    case "$(uname -s)" in
        Darwin*)
            open "$filename"
            ;;
        Linux*)
            if command -v mpg123 &> /dev/null; then
                mpg123 "$filename"
            elif command -v ffplay &> /dev/null; then
                ffplay -nodisp -autoexit "$filename"
            else
                xdg-open "$filename"
            fi
            ;;
        *)
            print_warning "Neznámy operačný systém"
            ;;
    esac
}

# Spustenie web servera
start_web_server() {
    local port="${1:-8000}"
    
    print_info "Spúšťam web server na porte $port..."
    print_info "Otvorte prehliadač na: http://localhost:$port"
    print_warning "Stlačte Ctrl+C pre zastavenie"
    
    python3 -m http.server $port
}

# Vytvorenie verejných URL
create_public_urls() {
    print_info "Vytváram verejné URL pre audio súbory..."
    
    gsutil ls -r gs://$BUCKET_NAME/**/*.mp3 gs://$BUCKET_NAME/**/*.wav 2>/dev/null | while read file; do
        if [ ! -z "$file" ]; then
            # Nastavenie verejného prístupu
            gsutil acl ch -u AllUsers:R "$file" 2>/dev/null
            
            # Vytvorenie verejnej URL
            public_url=$(echo "$file" | sed 's|gs://|https://storage.googleapis.com/|')
            filename=$(basename "$file")
            
            echo -e "${GREEN}🌐 $filename${NC}"
            echo -e "   $public_url"
        fi
    done
}

# Hlavné menu
show_menu() {
    echo -e "\n${CYAN}📋 Dostupné akcie:${NC}"
    echo "1. 📁 Zobraziť zoznam audio súborov"
    echo "2. 📥 Stiahnuť všetky audio súbory"
    echo "3. 🎵 Prehrať lokálny súbor"
    echo "4. 🌐 Spustiť web server"
    echo "5. 🌐 Vytvoriť verejné URL"
    echo "6. 🔧 Kontrola autentifikácie"
    echo "0. 🚪 Ukončiť"
    echo
}

# Kontrola autentifikácie
check_auth() {
    print_info "Kontrolujem autentifikáciu..."
    
    gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -1
    
    if [ $? -eq 0 ]; then
        print_success "Autentifikácia je aktívna"
    else
        print_error "Nie ste prihlásený"
        print_info "Prihláste sa pomocou: gcloud auth login"
    fi
}

# Hlavná funkcia
main() {
    print_header
    
    # Kontroly
    check_gcloud
    setup_project
    
    # Hlavná slučka
    while true; do
        show_menu
        read -p "👉 Vyberte akciu (0-6): " choice
        
        case $choice in
            1)
                list_audio_files
                ;;
            2)
                download_all_audio
                ;;
            3)
                echo "📁 Lokálne audio súbory:"
                ls -1 *.mp3 *.wav *.m4a 2>/dev/null | nl
                read -p "👉 Zadajte názov súboru: " filename
                if [ ! -z "$filename" ]; then
                    play_audio "$filename"
                fi
                ;;
            4)
                read -p "👉 Port (predvolený 8000): " port
                port=${port:-8000}
                start_web_server $port
                ;;
            5)
                create_public_urls
                ;;
            6)
                check_auth
                ;;
            0)
                print_success "Dovidenia!"
                exit 0
                ;;
            *)
                print_error "Neplatná voľba"
                ;;
        esac
        
        echo
        read -p "Stlačte Enter pre pokračovanie..."
    done
}

# Spustenie
main "$@"
