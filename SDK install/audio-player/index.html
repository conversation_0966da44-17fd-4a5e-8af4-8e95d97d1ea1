<!DOCTYPE html>
<html lang="sk">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Cloud Audio Player</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .audio-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #4285f4;
        }
        
        .audio-section h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.3em;
        }
        
        .audio-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        audio {
            flex: 1;
            min-width: 300px;
            height: 40px;
        }
        
        .btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .btn:hover {
            background: #3367d6;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.3);
        }
        
        .btn.success {
            background: #34a853;
        }
        
        .btn.success:hover {
            background: #2d8f47;
        }
        
        .cloud-files {
            background: #e8f0fe;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }
        
        .cloud-files h3 {
            color: #1a73e8;
            margin: 0 0 15px 0;
        }
        
        .file-list {
            display: grid;
            gap: 10px;
        }
        
        .file-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #dadce0;
            display: flex;
            justify-content: between;
            align-items: center;
        }
        
        .file-name {
            flex: 1;
            font-family: monospace;
            color: #333;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            background: #e8f5e8;
            border: 1px solid #c3e6c3;
            color: #2e7d32;
        }
        
        .error {
            background: #ffebee;
            border-color: #ffcdd2;
            color: #c62828;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎵 Google Cloud Audio Player</h1>
            <p>Prehrávanie audio súborov z Google Cloud Storage</p>
        </div>
        
        <div class="content">
            <div class="audio-section">
                <h3>🎼 Main Menu (WAV)</h3>
                <div class="audio-controls">
                    <audio controls preload="metadata">
                        <source src="mainmenu.wav" type="audio/wav">
                        Váš prehliadač nepodporuje audio element.
                    </audio>
                    <button class="btn" onclick="downloadFile('mainmenu.wav')">📥 Stiahnuť</button>
                </div>
            </div>
            
            <div class="audio-section">
                <h3>🎙️ Rozprávač 001 (MP3)</h3>
                <div class="audio-controls">
                    <audio controls preload="metadata">
                        <source src="rozpravac_001.mp3" type="audio/mpeg">
                        Váš prehliadač nepodporuje audio element.
                    </audio>
                    <button class="btn" onclick="downloadFile('rozpravac_001.mp3')">📥 Stiahnuť</button>
                </div>
            </div>
            
            <div class="cloud-files">
                <h3>☁️ Google Cloud Storage súbory</h3>
                <div class="file-list">
                    <div class="file-item">
                        <span class="file-name">gs://vanhelsing/mainmenu.wav</span>
                        <button class="btn success" onclick="streamFromCloud('gs://vanhelsing/mainmenu.wav')">▶️ Stream</button>
                    </div>
                    <div class="file-item">
                        <span class="file-name">gs://vanhelsing/ZVUKY/REC_KAPITOLA_1/rozpravac_001.mp3</span>
                        <button class="btn success" onclick="streamFromCloud('gs://vanhelsing/ZVUKY/REC_KAPITOLA_1/rozpravac_001.mp3')">▶️ Stream</button>
                    </div>
                </div>
                
                <button class="btn" onclick="loadAllFiles()" style="margin-top: 15px;">🔄 Načítať všetky súbory</button>
            </div>
            
            <div id="status" class="status" style="display: none;"></div>
        </div>
    </div>

    <script>
        function downloadFile(filename) {
            const link = document.createElement('a');
            link.href = filename;
            link.download = filename;
            link.click();
            showStatus(`Sťahujem súbor: ${filename}`, 'success');
        }
        
        function streamFromCloud(gsPath) {
            showStatus(`Streamujem z Google Cloud: ${gsPath}`, 'success');
            // Tu by sa implementovalo priame streamovanie z Google Cloud
            alert(`Streamovanie z: ${gsPath}\n\nPre priame streamovanie je potrebné nastaviť autentifikáciu a CORS politiky v Google Cloud Console.`);
        }
        
        function loadAllFiles() {
            showStatus('Načítavam zoznam všetkých audio súborov z Google Cloud...', 'info');
            // Tu by sa implementovalo načítanie všetkých súborov cez gcloud API
            setTimeout(() => {
                showStatus('Funkcia bude implementovaná s Google Cloud API', 'info');
            }, 1000);
        }
        
        function showStatus(message, type = 'success') {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = `status ${type === 'error' ? 'error' : ''}`;
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }
        
        // Inicializácia
        document.addEventListener('DOMContentLoaded', function() {
            showStatus('Google Cloud Audio Player pripravený!', 'success');
            
            // Kontrola dostupnosti audio súborov
            const audioElements = document.querySelectorAll('audio');
            audioElements.forEach((audio, index) => {
                audio.addEventListener('loadedmetadata', () => {
                    console.log(`Audio ${index + 1} načítané úspešne`);
                });
                
                audio.addEventListener('error', () => {
                    console.error(`Chyba pri načítavaní audio ${index + 1}`);
                });
            });
        });
    </script>
</body>
</html>
