# 🎵 Google Cloud Audio SDK

Kompletné riešenie pre prehrávanie audio súborov z Google Cloud Storage vo vašom počítači.

## ✅ Čo je nainštalované

### Google Cloud SDK
- **Verzia**: 526.0.1 (najn<PERSON><PERSON>ia)
- **Komponenty**: gcloud, gsutil, bq
- **Projekt**: podcast-central-xl9zv
- **Bucket**: vanhelsing
- **Účet**: <EMAIL>

### Audio Player Tools
- **Web prehrávač**: HTML5 audio player s moderným dizajnom
- **Python manager**: Interaktívny nástroj pre správu audio súborov
- **Bash skript**: Príkazový riadok nástroj
- **HTTP server**: Lokálny web server pre prehrávanie

## 🚀 Ako používať

### 1. Web Audio Player
```bash
cd "SDK install/audio-player"
python3 -m http.server 8000
```
Potom otvorte: http://localhost:8000

### 2. Python Audio Manager
```bash
cd "SDK install/audio-player"
python3 cloud_audio_manager.py
```

### 3. Bash Script
```bash
cd "SDK install/audio-player"
./gcloud-audio.sh
```

### 4. Priame príkazy
```bash
# Zoznam súborov
gsutil ls gs://vanhelsing/ZVUKY/

# Stiahnutie súboru
gsutil cp gs://vanhelsing/mainmenu.wav .

# Prehranie súboru (macOS)
open mainmenu.wav
```

## 📁 Dostupné audio súbory

### Van Helsing Project
- **Bucket**: gs://vanhelsing/
- **Main menu**: mainmenu.wav (13.6 MB)
- **Kapitola 1**: 25 MP3 súborov (kocis_001-004, rozpravac_001-021)

### Typy súborov
- ✅ **MP3**: Komprimované audio (300KB - 1MB)
- ✅ **WAV**: Nekomprimované audio (13.6MB)
- ✅ **M4A**: Apple audio formát

## 🔧 Riešenie problémov

### Autentifikácia
```bash
# Kontrola prihlásenia
gcloud auth list

# Nové prihlásenie
gcloud auth login

# Nastavenie projektu
gcloud config set project podcast-central-xl9zv
```

### CORS problémy
```bash
# Nastavenie verejného prístupu
gsutil acl ch -u AllUsers:R gs://vanhelsing/mainmenu.wav

# Verejná URL
https://storage.googleapis.com/vanhelsing/mainmenu.wav
```

### Audio prehrávanie
- **Chrome**: Plná podpora HTML5 audio
- **Safari**: Podpora WAV a MP3
- **Firefox**: Podpora MP3 a WAV
- **Systémový prehrávač**: `open` (macOS), `xdg-open` (Linux)

## 🌐 Web Features

### HTML5 Audio Player
- ✅ Moderný dizajn s Google Cloud témou
- ✅ Podpora WAV a MP3 formátov
- ✅ Download funkcia
- ✅ Streaming z Google Cloud
- ✅ Responsive design

### Python Manager
- ✅ Interaktívne menu
- ✅ Automatické sťahovanie
- ✅ Systémové prehrávanie
- ✅ Verejné URL generovanie
- ✅ HTTP server

### Bash Script
- ✅ Farebný výstup
- ✅ Batch operácie
- ✅ Chybové hlásenia
- ✅ Cross-platform podpora

## 📊 Štatistiky

```
Projekty: 4 (epot-lesa, forest-echoes, podcast-central-xl9zv, sepot-lesa)
Buckets: 2 (podcast-central-xl9zv.firebasestorage.app, vanhelsing)
Audio súbory: 26 (1 WAV + 25 MP3)
Celková veľkosť: ~20 MB
```

## 🎯 Ďalšie možnosti

### Streaming
- Priame prehrávanie z Google Cloud bez sťahovania
- Podpora pre veľké audio súbory
- Automatické cache-ovanie

### Batch operácie
- Stiahnutie všetkých súborov naraz
- Konverzia formátov
- Automatické organizovanie

### API integrácia
- Google Cloud Storage API
- Programatické ovládanie
- Webhook notifikácie

## 🔗 Užitočné odkazy

- [Google Cloud Console](https://console.cloud.google.com/)
- [Cloud Storage Browser](https://console.cloud.google.com/storage/browser)
- [Audio súbory](https://console.cloud.google.com/storage/browser/vanhelsing)
- [Lokálny prehrávač](http://localhost:8000)

---

**✅ SDK je úspešne nainštalované a pripravené na použitie!**

Všetky nástroje sú funkčné a audio súbory z Google Cloud Storage sa dajú prehrávať vo vašom počítači bez obmedzení.
